# Filepath: snippets/unSigned/updateAgentWithPersistent.py
import subprocess
import platform
import os
import json
from logzero import logger

def getDeviceConfig(configFile):
    logger.info(f'Attempting to read config file: {configFile}...')
    if os.path.isfile(configFile):
        try:
            with open(configFile, 'r') as f:
                configDict = json.load(f)
            logger.info(f'Successfully read {configFile}')
            deviceUuid = configDict['deviceuuid']

            # The config file doesn't store groupuuid, we need to get it from the device registration
            # For now, we'll use a placeholder and let the script handle device lookup
            # The installAgent.sh script only needs groupuuid for NEW registrations
            # For existing devices, it will skip registration and just update
            groupUuid = 'existing-device'  # Placeholder for existing installations

            logger.info(f'Device UUID: {deviceUuid}')
            return deviceUuid, groupUuid, configDict
        except Exception as e:
            logger.error(f'Failed to read {configFile}: {e}')
            return None, None, None
    else:
        logger.error(f'{configFile} does not exist.')
        return None, None, None

def getAppDirs():
    if platform.system() == 'Windows':
        appDir = 'c:\\program files (x86)\\Wegweiser\\'
    else:
        appDir = '/opt/Wegweiser/'
    configDir = os.path.join(appDir, 'Config', '')
    return appDir, configDir

def runAgentUpdate():
    """Run the enhanced installAgent.sh script to update both agents"""
    
    # Only run on Linux systems
    if platform.system() != 'Linux':
        logger.info('Enhanced agent update only supported on Linux systems')
        return False
    
    # Get device configuration
    appDir, configDir = getAppDirs()
    configFile = os.path.join(configDir, 'agent.config')
    deviceUuid, groupUuid, configDict = getDeviceConfig(configFile)

    if not deviceUuid:
        logger.error('Could not determine device UUID from config')
        return False

    logger.info(f'Updating agent for device {deviceUuid}')

    try:
        # Use a writable location and fix the permission issue
        # Create a unique temp file name to avoid conflicts
        import tempfile
        temp_script = tempfile.mktemp(suffix='.sh', prefix='installAgent_')

        # Download and run the enhanced installation script
        # Since this is an existing device, we can use any group UUID - the script will skip registration
        command = f'curl -o {temp_script} https://app.wegweiser.tech/download/installAgent.sh && chmod +x {temp_script} && sudo {temp_script} existing-device-update'
        
        logger.info(f'Running command: {command}')
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info('Agent update completed successfully')
            logger.info(f'Update output: {result.stdout}')
            
            # Check if persistent agent service is running
            try:
                service_status = subprocess.run(['sudo', 'systemctl', 'is-active', 'wegweiser-persistent-agent.service'], 
                                              capture_output=True, text=True)
                if service_status.returncode == 0:
                    logger.info('Persistent agent service is running')
                else:
                    logger.warning('Persistent agent service is not running')
            except Exception as e:
                logger.error(f'Could not check persistent agent service status: {e}')
            
            return True
        else:
            logger.error(f'Agent update failed with return code {result.returncode}')
            logger.error(f'Error output: {result.stderr}')
            return False
            
    except subprocess.TimeoutExpired:
        logger.error('Agent update timed out after 5 minutes')
        return False
    except Exception as e:
        logger.error(f'Error running agent update: {e}')
        return False

####################### MAIN #######################

logger.info('Starting enhanced agent update (cron + persistent)')

success = runAgentUpdate()

if success:
    logger.info('Enhanced agent update completed successfully')
    logger.info('Both cron-based and persistent agents have been updated')
    logger.info('The persistent agent service should now be running with the latest code')
else:
    logger.error('Enhanced agent update failed')
    logger.info('Check the logs above for details')
